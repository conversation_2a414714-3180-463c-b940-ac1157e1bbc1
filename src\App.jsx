import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import { useEffect, useState } from 'react';
import Navbar from './components/Navbar';
import { performanceMonitor, trackPageLoad } from './services/performanceMonitoring.js';
import { preloadCriticalData } from './services/apiOptimization.js';
import HomePage from './pages/HomePage';
import ProductPage from './pages/ProductPage';
import CartPage from './pages/CartPage';
import CheckoutAddressPage from './pages/CheckoutAddressPage';
import CheckoutPaymentPage from './pages/CheckoutPaymentPage';
import PaymentSuccessPage from './pages/PaymentSuccessPage';
import OrderSuccessPage from './pages/OrderSuccessPage';
import Checkout from './pages/Checkout';
import AboutPage from './pages/AboutPage';
import CategoryPage from './pages/CategoryPage';
import CollectionsPage from './pages/CollectionsPage';
import SearchPage from './pages/SearchPage';
import WishlistPage from './pages/WishlistPage';
import BestSellersPage from './pages/BestSellersPage';
import DealsPage from './pages/DealsPage';
import LoginPage from './pages/LoginPage';
import WhatsAppLoginPage from './pages/WhatsAppLoginPage';
import ResetPasswordPage from './pages/ResetPasswordPage';
import ProfilePage from './pages/ProfilePage';
import OrdersPage from './pages/OrdersPage';
import TermsPage from './pages/TermsPage';
import PrivacyPage from './pages/PrivacyPage';
import TrackOrderPage from './pages/TrackOrderPage';
import ReturnsPage from './pages/ReturnsPage';
import ContactPage from './pages/ContactPage';
import ProtectedRoute from './components/auth/ProtectedRoute';
import Footer from './components/Footer';
import LoadingAnimation from './components/LoadingAnimation';
import RouteLoadingAnimation from './components/RouteLoadingAnimation';
import NotificationContainer from './components/ui/Notification';
import { CartProvider } from './context/CartContext';
import { WishlistProvider } from './context/WishlistContext';
import { AuthProvider } from './context/AuthContext';
import { NotificationProvider } from './context/NotificationContext';
import ErrorBoundary from './components/ErrorBoundary';

import { AnimatePresence } from 'framer-motion';
import PageTransition from './components/PageTransition';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import './index.css';

// Simple wrapper that provides AuthProvider first, then CartProvider
const AuthCartWrapper = ({ children }) => {
  return (
    <AuthProvider>
      <CartProvider>
        {children}
      </CartProvider>
    </AuthProvider>
  );
};

export function App() {
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [isRouteLoading, setIsRouteLoading] = useState(false);



  useEffect(() => {
    // Initialize performance monitoring and preload critical data
    // performanceMonitor.monitorResourceLoading();
    // performanceMonitor.monitorLongTasks();

    // Preload critical API data
    preloadCriticalData().catch(console.error);

    // Track page load performance
    // window.addEventListener('load', trackPageLoad);

    // Add Inter and Montserrat fonts
    const interLink = document.createElement('link');

    interLink.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap';
    interLink.rel = 'stylesheet';
    document.head.appendChild(interLink);

    const montserratLink = document.createElement('link');
    montserratLink.href = 'https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&display=swap';
    montserratLink.rel = 'stylesheet';
    document.head.appendChild(montserratLink);

    // Add Bebas Neue for streetwear aesthetic
    const bebasNeueLink = document.createElement('link');
    bebasNeueLink.href = 'https://fonts.googleapis.com/css2?family=Bebas+Neue&display=swap';
    bebasNeueLink.rel = 'stylesheet';
    document.head.appendChild(bebasNeueLink);

    // Set dark mode by default
    document.documentElement.classList.add('dark');
  }, []);

  const handleInitialLoadingComplete = () => {
    setIsInitialLoading(false);
  };

  const handleRouteLoadingStart = () => {
    setIsRouteLoading(true);
  };

  const handleRouteLoadingComplete = () => {
    setIsRouteLoading(false);
  };

  return (
    <ErrorBoundary>
      <NotificationProvider>
        <AuthCartWrapper>
          <WishlistProvider>
            {/* <OutfitProvider> */}
            <Router>
              {/* Initial loading animation */}
              {isInitialLoading && (
                <LoadingAnimation onComplete={handleInitialLoadingComplete} />
              )}
              {/* Route loading animation */}
              {isRouteLoading && (
                <RouteLoadingAnimation onComplete={handleRouteLoadingComplete} />
              )}
              <div className="min-h-screen bg-black text-[#d4d4d4] font-['Inter',sans-serif]">
                <Navbar />
                <main className="pt-16">
                  <AppRoutes
                    onRouteLoadingStart={handleRouteLoadingStart}
                    onRouteLoadingComplete={handleRouteLoadingComplete}
                  />
                </main>
                <Footer />
                {/* <OutfitIndicator /> */}
                <NotificationContainer />
              </div>
            </Router>
            {/* </OutfitProvider> */}
          </WishlistProvider>
        </AuthCartWrapper>
      </NotificationProvider>
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="dark"
      />
    </ErrorBoundary>
  );
}

// Component to scroll to top on route change
function ScrollToTop() {
  const location = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location.pathname]);

  return null;
}

// Separate component to access location for AnimatePresence
function AppRoutes({ onRouteLoadingStart, onRouteLoadingComplete }) {
  const location = useLocation();
  const [previousPath, setPreviousPath] = useState(location.pathname);

  useEffect(() => {
    // Only show route loading if the path actually changed (not initial load)
    if (location.pathname !== previousPath) {
      onRouteLoadingStart();

      // Simulate loading time for better UX
      const timer = setTimeout(() => {
        onRouteLoadingComplete();
      }, 1500); // Balanced duration for good branding and speed

      setPreviousPath(location.pathname);
      return () => clearTimeout(timer);
    }
  }, [location.pathname, previousPath, onRouteLoadingStart, onRouteLoadingComplete]);

  return (
    <>
      <ScrollToTop />
      <AnimatePresence mode="wait">
        <Routes location={location} key={location.pathname}>
        <Route
          path="/"
          element={
            <PageTransition>
              <HomePage />
            </PageTransition>
          }
        />
        <Route
          path="/collections"
          element={
            <PageTransition>
              <CollectionsPage />
            </PageTransition>
          }
        />
        <Route
          path="/product/:id"
          element={
            <PageTransition>
              <ProductPage />
            </PageTransition>
          }
        />
        <Route
          path="/cart"
          element={
            <PageTransition>
              <CartPage />
            </PageTransition>
          }
        />
        <Route
          path="/checkout/address"
          element={
            <ProtectedRoute requireAuth={true}>
              <PageTransition>
                <CheckoutAddressPage key="checkout-address" />
              </PageTransition>
            </ProtectedRoute>
          }
        />
        <Route
          path="/checkout/payment"
          element={
            <ProtectedRoute requireAuth={true}>
              <PageTransition>
                <CheckoutPaymentPage key="checkout-payment" />
              </PageTransition>
            </ProtectedRoute>
          }
        />
        <Route
          path="/payment/success"
          element={
            <ProtectedRoute requireAuth={true}>
              <PageTransition>
                <PaymentSuccessPage key="payment-success" />
              </PageTransition>
            </ProtectedRoute>
          }
        />
        <Route
          path="/order-success"
          element={
              <PageTransition>
                <OrderSuccessPage />
              </PageTransition>
          }
        />
        <Route
          path="/checkout"
          element={
              <PageTransition>
                <Checkout />
              </PageTransition>
          }
        />
        <Route
          path="/about"
          element={
            <PageTransition>
              <AboutPage />
            </PageTransition>
          }
        />
        <Route
          path="/category/:category"
          element={
            <PageTransition>
              <CategoryPage />
            </PageTransition>
          }
        />
        <Route
          path="/search"
          element={
            <PageTransition>
              <SearchPage />
            </PageTransition>
          }
        />
        <Route
          path="/wishlist"
          element={
            <PageTransition>
              <WishlistPage />
            </PageTransition>
          }
        />
        {/* <Route
          path="/outfits"
          element={
            <PageTransition>
              <OutfitsPage />
            </PageTransition>
          }
        /> */}
        <Route
          path="/bestsellers"
          element={
            <PageTransition>
              <BestSellersPage />
            </PageTransition>
          }
        />
        <Route
          path="/deals"
          element={
            <PageTransition>
              <DealsPage />
            </PageTransition>
          }
        />
        <Route
          path="/login"
          element={
            <ProtectedRoute requireAuth={false}>
              <PageTransition>
                <LoginPage />
              </PageTransition>
            </ProtectedRoute>
          }
        />
        <Route
          path="/whatsapp-login"
          element={
            <ProtectedRoute requireAuth={false}>
              <PageTransition>
                <WhatsAppLoginPage />
              </PageTransition>
            </ProtectedRoute>
          }
        />
        <Route
          path="/reset-password/:token"
          element={
            <ProtectedRoute requireAuth={false}>
              <PageTransition>
                <ResetPasswordPage />
              </PageTransition>
            </ProtectedRoute>
          }
        />
        <Route
          path="/profile"
          element={
            <ProtectedRoute requireAuth={true}>
              <PageTransition>
                <ProfilePage />
              </PageTransition>
            </ProtectedRoute>
          }
        />
        <Route
          path="/orders"
          element={
            <ProtectedRoute requireAuth={true}>
              <PageTransition>
                <OrdersPage />
              </PageTransition>
            </ProtectedRoute>
          }
        />
        <Route
          path="/terms"
          element={
            <PageTransition>
              <TermsPage />
            </PageTransition>
          }
        />
        <Route
          path="/privacy"
          element={
            <PageTransition>
              <PrivacyPage />
            </PageTransition>
          }
        />
        <Route
          path="/track-order"
          element={
            <PageTransition>
              <TrackOrderPage />
            </PageTransition>
          }
        />
        <Route
          path="/returns"
          element={
            <PageTransition>
              <ReturnsPage />
            </PageTransition>
          }
        />
        <Route
          path="/contact"
          element={
            <PageTransition>
              <ContactPage />
            </PageTransition>
          }
        />
      </Routes>
    </AnimatePresence>
    </>
  );
}

export default App;