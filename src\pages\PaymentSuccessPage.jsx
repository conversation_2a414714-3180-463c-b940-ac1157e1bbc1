import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import { CheckCircle, Package, MapPin, Calendar, ArrowRight, Home } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { useCart } from '../context/CartContext';
import { toast } from 'react-toastify';
import axios from 'axios';

const PaymentSuccessPage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { user } = useAuth();
  const { items: cartItems, clearCart } = useCart();
  const [isVerifying, setIsVerifying] = useState(true);
  const [orderData, setOrderData] = useState(null);

  // Debug: Log that component is mounting
  console.log('🔄 PaymentSuccessPage component mounted');

  useEffect(() => {
    const verifyPayment = async () => {
      try {
        console.log('🔄 PaymentSuccessPage - Starting payment verification');
        console.log('🔄 PaymentSuccessPage - URL search params:', window.location.search);

        // Get URL parameters (PhonePe sends data via URL)
        const urlParams = new URLSearchParams(window.location.search);
        console.log('🔄 PaymentSuccessPage - URL params:', Object.fromEntries(urlParams));

        // Get stored data from localStorage
        let orderData = localStorage.getItem('phonepe_order_data');
        let merchantTransactionId = localStorage.getItem('phonepe_merchant_transaction_id');
        let authToken = localStorage.getItem('phonepe_auth_token');

        // Fallback: Get data from URL parameters if localStorage is cleared
        const urlMerchantTransactionId = urlParams.get('merchant_transaction_id');
        const urlUserId = urlParams.get('user_id');

        if (!merchantTransactionId && urlMerchantTransactionId) {
          console.log('🔄 Using merchant transaction ID from URL params');
          merchantTransactionId = urlMerchantTransactionId;
        }

        // If no phonepe auth token, try to get from regular auth storage
        if (!authToken) {
          const storedTokens = localStorage.getItem('wolffoxx_tokens');
          if (storedTokens) {
            const tokens = JSON.parse(storedTokens);
            authToken = tokens.access_token || tokens.accessToken;
          }
        }

        console.log('🔄 PaymentSuccessPage - Retrieved data:', {
          hasOrderData: !!orderData,
          hasMerchantTransactionId: !!merchantTransactionId,
          hasAuthToken: !!authToken,
          urlMerchantTransactionId,
          urlUserId
        });

        if (!merchantTransactionId || !authToken) {
          console.error('Missing payment data - merchant transaction ID or auth token');
          toast.error('Payment verification failed. Missing payment data.');
          navigate('/cart');
          return;
        }

        const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1';

        console.log('🔄 Processing PhonePe payment success with merchant transaction ID:', merchantTransactionId);

        // Prepare verification data
        const verificationData = {
          merchant_transaction_id: merchantTransactionId
        };

        // Include order data if available from localStorage
        if (orderData) {
          try {
            verificationData.order_data = JSON.parse(orderData);
          } catch (error) {
            console.warn('Failed to parse order data from localStorage:', error);
          }
        }

        // Since user reached success page, assume payment is successful
        // PhonePe only redirects here on successful payment
        const verifyResponse = await axios.post(`${API_BASE_URL}/payments/verify`, verificationData, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`
          }
        });

        console.log('🔄 Payment verification response:', verifyResponse.data);

        if (verifyResponse.data && verifyResponse.data.success && verifyResponse.data.data) {
          // Payment successful
          const orderDetails = verifyResponse.data.data;
          
          // Store order data for success display
          const orderSuccessData = {
            id: orderDetails.order_id,
            order_number: orderDetails.order_number,
            payment_method: 'phonepe',
            total_amount: orderDetails.total_amount,
            payment_status: orderDetails.payment_status,
            shipping_address: JSON.parse(localStorage.getItem('phonepe_order_data') || '{}').shipping_address
          };

          setOrderData(orderSuccessData);

          // Clean up PhonePe specific localStorage items
          localStorage.removeItem('phonepe_order_data');
          localStorage.removeItem('phonepe_merchant_transaction_id');
          localStorage.removeItem('phonepe_auth_token');

          // Clear cart if items exist
          if (cartItems.length > 0) {
            console.log('🧹 Clearing cart on payment success');
            clearCart();
          }

          toast.success('Payment successful! Order placed.');

        } else {
          // Payment verification failed
          console.error('❌ Payment verification failed:', verifyResponse.data);
          toast.error('Payment verification failed. Please contact support.');
        }

      } catch (error) {
        console.error('❌ Payment verification error:', error);
        toast.error('Payment verification failed. Please contact support.');
      } finally {
        setIsVerifying(false);
      }
    };

    verifyPayment();
  }, [navigate, searchParams]);

  if (isVerifying) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="bg-gray-900 p-8 rounded-lg shadow-md text-center max-w-md w-full mx-4">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-white mb-2">Verifying Payment</h2>
          <p className="text-gray-400">Please wait while we verify your payment...</p>
        </div>
      </div>
    );
  }

  if (!orderData) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="bg-gray-900 p-8 rounded-lg shadow-md text-center max-w-md w-full mx-4">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-white mb-2">Payment Verification Failed</h2>
          <p className="text-gray-400 mb-6">We could not verify your payment. Please contact support.</p>
          <div className="space-y-3">
            <button
              onClick={() => navigate('/cart')}
              className="w-full bg-orange-500 text-white py-2 px-4 rounded-md hover:bg-orange-600 transition-colors"
            >
              Return to Cart
            </button>
            <button
              onClick={() => navigate('/')}
              className="w-full bg-gray-700 text-white py-2 px-4 rounded-md hover:bg-gray-600 transition-colors"
            >
              Return to Home
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="container mx-auto px-4 py-8">
        {/* Success Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6"
          >
            <CheckCircle className="w-12 h-12 text-white" />
          </motion.div>

          <h1 className="text-4xl font-bold mb-4">Order Confirmed!</h1>
          <p className="text-xl text-gray-300 mb-2">
            Thank you for your purchase, {user?.first_name || 'Customer'}!
          </p>
          <p className="text-gray-400">
            Your order has been successfully placed and payment confirmed via PhonePe.
          </p>
        </motion.div>

        {/* Order Details */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
          className="max-w-4xl mx-auto"
        >
          <div className="bg-gray-900 rounded-lg p-8 mb-8">
            <div className="grid md:grid-cols-2 gap-8">
              {/* Order Info */}
              <div>
                <h2 className="text-2xl font-semibold mb-6 flex items-center">
                  <Package className="w-6 h-6 mr-3 text-orange-500" />
                  Order Details
                </h2>

                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Order Number:</span>
                    <span className="font-semibold">{orderData.order_number}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-gray-400">Payment Method:</span>
                    <span className="font-semibold">PhonePe</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-gray-400">Total Amount:</span>
                    <span className="font-semibold text-green-500">₹{orderData.total_amount}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-gray-400">Order Date:</span>
                    <span className="font-semibold">{new Date().toLocaleDateString()}</span>
                  </div>
                </div>
              </div>

              {/* Shipping Info */}
              <div>
                <h2 className="text-2xl font-semibold mb-6 flex items-center">
                  <MapPin className="w-6 h-6 mr-3 text-orange-500" />
                  Shipping Address
                </h2>

                {orderData.shipping_address && (
                  <div className="text-gray-300 space-y-2">
                    <p>{orderData.shipping_address.line1}</p>
                    {orderData.shipping_address.line2 && <p>{orderData.shipping_address.line2}</p>}
                    <p>{orderData.shipping_address.city}, {orderData.shipping_address.state}</p>
                    <p>{orderData.shipping_address.postal_code}</p>
                    <p>{orderData.shipping_address.country}</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Next Steps */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="bg-gray-900 rounded-lg p-8 mb-8"
          >
            <h2 className="text-2xl font-semibold mb-6 flex items-center">
              <Calendar className="w-6 h-6 mr-3 text-orange-500" />
              What's Next?
            </h2>

            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white font-bold">1</span>
                </div>
                <h3 className="font-semibold mb-2">Order Processing</h3>
                <p className="text-gray-400 text-sm">We'll prepare your order for shipment</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white font-bold">2</span>
                </div>
                <h3 className="font-semibold mb-2">Shipping</h3>
                <p className="text-gray-400 text-sm">Your order will be shipped within 2-3 business days</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white font-bold">3</span>
                </div>
                <h3 className="font-semibold mb-2">Delivery</h3>
                <p className="text-gray-400 text-sm">Estimated delivery in 5-7 business days</p>
              </div>
            </div>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.6 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <button
              onClick={() => navigate('/orders')}
              className="bg-orange-500 hover:bg-orange-600 text-white px-8 py-3 rounded-lg font-semibold transition-colors flex items-center justify-center"
            >
              <Package className="w-5 h-5 mr-2" />
              Track Your Order
              <ArrowRight className="w-5 h-5 ml-2" />
            </button>

            <button
              onClick={() => navigate('/')}
              className="bg-gray-700 hover:bg-gray-600 text-white px-8 py-3 rounded-lg font-semibold transition-colors flex items-center justify-center"
            >
              <Home className="w-5 h-5 mr-2" />
              Continue Shopping
            </button>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default PaymentSuccessPage;
