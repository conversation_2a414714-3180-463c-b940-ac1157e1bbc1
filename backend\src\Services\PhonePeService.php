<?php

namespace Wolffoxx\Services;

/**
 * PhonePe Payment Gateway Service
 * 
 * Handles PhonePe payment integration including:
 * - Payment request creation
 * - Payment verification
 * - Signature generation and validation
 */
class PhonePeService
{
    private string $merchantId;
    private string $saltKey;
    private int $saltIndex;
    private string $baseUrl;
    private string $environment;

    public function __construct()
    {
        try {
            // Use updated UAT credentials
            $this->merchantId = $_ENV['PHONEPE_MERCHANT_ID'] ?? 'PGTESTPAYUAT86';
            $this->saltKey = $_ENV['PHONEPE_SALT_KEY'] ?? '96434309-7796-489d-8924-ab56988a6076';
            $this->saltIndex = (int)($_ENV['PHONEPE_SALT_INDEX'] ?? 1);
            $this->environment = $_ENV['PHONEPE_ENVIRONMENT'] ?? 'UAT';

            if ($this->environment === 'PRODUCTION') {
                $this->baseUrl = $_ENV['PHONEPE_PRODUCTION_BASE_URL'] ?? 'https://api.phonepe.com/apis/hermes';
            } else {
                $this->baseUrl = $_ENV['PHONEPE_UAT_BASE_URL'] ?? 'https://api-preprod.phonepe.com/apis/pg-sandbox';
            }

            error_log('PhonePe Service initialized - Merchant ID: ' . $this->merchantId . ', Environment: ' . $this->environment . ', Base URL: ' . $this->baseUrl);
        } catch (\Exception $e) {
            error_log('PhonePe Service initialization failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create payment request
     * Corrected endpoint and signature generation
     */
    public function createPaymentRequest(array $paymentData): ?array
    {
        try {
            $merchantTransactionId = 'MT' . time() . rand(1000, 9999);
            
            // Add merchant transaction ID to redirect URL for localStorage fallback
            $redirectUrl = $paymentData['redirect_url'] . '?merchant_transaction_id=' . $merchantTransactionId . '&user_id=' . $paymentData['user_id'];

            $payload = [
                'merchantId' => $this->merchantId,
                'merchantTransactionId' => $merchantTransactionId,
                'merchantUserId' => 'MUID' . $paymentData['user_id'],
                'amount' => $paymentData['amount'], // Amount in paise
                'redirectUrl' => $redirectUrl,
                'redirectMode' => 'GET',
                'callbackUrl' => $paymentData['callback_url'],
                'mobileNumber' => $paymentData['mobile_number'] ?? null,
                'paymentInstrument' => [
                    'type' => 'PAY_PAGE'
                ]
            ];

            error_log('PhonePe Payment Payload: ' . json_encode($payload));

            $base64Payload = base64_encode(json_encode($payload));
            
            // ✅ Fixed: Correct endpoint for signature generation
            $signature = $this->generateSignature($base64Payload, '/pg/v1/pay');

            // ✅ Fixed: Correct API endpoint
            $url = $this->baseUrl . '/pg/v1/pay';
            
            // ✅ Fixed: Removed unnecessary X-MERCHANT-ID header
            $headers = [
                'Content-Type: application/json',
                'X-VERIFY: ' . $signature,
                'accept: application/json'
            ];

            $requestData = [
                'request' => $base64Payload
            ];

            error_log('PhonePe API Request URL: ' . $url);
            error_log('PhonePe API Request Headers: ' . json_encode($headers));
            error_log('PhonePe API Request Data: ' . json_encode($requestData));

            $response = $this->makeApiCall($url, json_encode($requestData), $headers);
            
            error_log('PhonePe API Response: ' . json_encode($response));

            if ($response && isset($response['success']) && $response['success']) {
                return [
                    'success' => true,
                    'merchantTransactionId' => $merchantTransactionId,
                    'transactionId' => $response['data']['transactionId'] ?? null,
                    'paymentUrl' => $response['data']['instrumentResponse']['redirectInfo']['url'] ?? null,
                    'response' => $response
                ];
            }

            error_log('PhonePe Payment Request Error: ' . json_encode($response));
            return [
                'success' => false,
                'error' => $response['message'] ?? 'Payment request failed',
                'response' => $response
            ];
        } catch (\Exception $e) {
            error_log('PhonePe Payment Request Exception: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Verify payment status
     * ✅ Fixed: Correct status endpoint
     */
    public function verifyPayment(string $merchantTransactionId): ?array
    {
        try {
            // ✅ Fixed: Correct status endpoint format
            $endpoint = '/pg/v1/status/' . $this->merchantId . '/' . $merchantTransactionId;
            $url = $this->baseUrl . $endpoint;
            $signature = $this->generateSignature('', $endpoint);

            $headers = [
                'Content-Type: application/json',
                'X-VERIFY: ' . $signature,
                'accept: application/json'
            ];

            error_log('PhonePe Status Check URL: ' . $url);
            error_log('PhonePe Status Check Headers: ' . json_encode($headers));

            $response = $this->makeApiCall($url, '', $headers, 'GET');

            if ($response && isset($response['success'])) {
                return [
                    'success' => $response['success'],
                    'status' => $response['data']['state'] ?? 'UNKNOWN',
                    'transactionId' => $response['data']['transactionId'] ?? null,
                    'amount' => $response['data']['amount'] ?? null,
                    'paymentInstrument' => $response['data']['paymentInstrument'] ?? null,
                    'response' => $response
                ];
            }

            error_log('PhonePe Payment Verification Error: ' . json_encode($response));
            return [
                'success' => false,
                'error' => $response['message'] ?? 'Payment verification failed',
                'response' => $response
            ];
        } catch (\Exception $e) {
            error_log('PhonePe Payment Verification Exception: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Generate signature for PhonePe API calls
     */
    private function generateSignature(string $payload, string $endpoint): string
    {
        $string = $payload . $endpoint . $this->saltKey;
        $hash = hash('sha256', $string);
        return $hash . '###' . $this->saltIndex;
    }

    /**
     * Verify callback signature from PhonePe
     */
    public function verifyCallbackSignature(string $response, string $signature): bool
    {
        try {
            $expectedSignature = hash('sha256', $response . $this->saltKey) . '###' . $this->saltIndex;
            return hash_equals($expectedSignature, $signature);
        } catch (\Exception $e) {
            error_log('PhonePe Signature Verification Exception: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Make API call to PhonePe
     */
    private function makeApiCall(string $url, string $data, array $headers, string $method = 'POST'): ?array
    {
        try {
            $curl = curl_init();

            curl_setopt_array($curl, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => $method,
                CURLOPT_HTTPHEADER => $headers,
                CURLOPT_SSL_VERIFYPEER => false, // For UAT testing
                CURLOPT_SSL_VERIFYHOST => false  // For UAT testing
            ]);

            if ($method === 'POST' && !empty($data)) {
                curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
            }

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $error = curl_error($curl);

            curl_close($curl);

            error_log('PhonePe API Response Code: ' . $httpCode);
            error_log('PhonePe API Response Body: ' . $response);

            if ($error) {
                error_log('PhonePe API cURL Error: ' . $error);
                return null;
            }

            $decodedResponse = json_decode($response, true);
            if (!$decodedResponse) {
                error_log('PhonePe API Invalid JSON Response: ' . $response);
                return null;
            }

            return $decodedResponse;
        } catch (\Exception $e) {
            error_log('PhonePe API Call Exception: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get merchant configuration
     */
    public function getMerchantConfig(): array
    {
        return [
            'merchantId' => $this->merchantId,
            'environment' => $this->environment,
            'baseUrl' => $this->baseUrl
        ];
    }
}
