<?php

namespace <PERSON>oxx\Controllers;

use Wolffoxx\Models\Order;
use Wolffoxx\Models\Cart;
use Wolffoxx\Models\User;
use Wolffoxx\Models\UserAddress;
use Wolffoxx\Services\NotificationService;
use Wolffoxx\Services\EmailService;
use Wolffoxx\Services\PhonePeService;
use Wolffoxx\Utils\Response;
use Wolffoxx\Middleware\AuthMiddleware;

/**
 * Payment Controller
 *
 * Handles PhonePe payment integration
 */
class PaymentController
{
    private Order $orderModel;
    private Cart $cartModel;
    private User $userModel;
    private NotificationService $notificationService;
    private EmailService $emailService;
    private PhonePeService $phonePeService;
    private UserAddress $addressModel;

    public function __construct()
    {
        $this->orderModel = new Order();
        $this->cartModel = new Cart();
        $this->userModel = new User();
        $this->addressModel = new UserAddress();
        $this->notificationService = new NotificationService();
        $this->emailService = new EmailService();
        $this->phonePeService = new PhonePeService();
    }

    /**
     * Create PhonePe payment request
     * POST /api/v1/payments/create-order
     */
    public function createPhonePeOrder(): void
    {
        error_log('PaymentController::createPhonePeOrder - Method called successfully');

        try {
            error_log('PaymentController::createPhonePeOrder - Starting payment order creation');

            // Test PhonePe service instantiation first
            if (!$this->phonePeService) {
                error_log('PaymentController::createPhonePeOrder - PhonePe service not initialized');
                Response::error('Payment service not available', 500);
                return;
            }

            $user = AuthMiddleware::getCurrentUserData();
            if (!$user) {
                error_log('PaymentController::createPhonePeOrder - Authentication failed');
                Response::error('Authentication required', 401);
                return;
            }

            error_log('PaymentController::createPhonePeOrder - User authenticated: ' . $user['id']);

            $input = json_decode(file_get_contents('php://input'), true);
            error_log('PaymentController::createPhonePeOrder - Input received: ' . json_encode($input));

            // Validate required fields
            if (empty($input['amount'])) {
                error_log('PaymentController::createPhonePeOrder - Missing amount');
                Response::error('Amount is required', 400);
                return;
            }

            // Get user's cart to validate amount
            error_log('PaymentController::createPhonePeOrder - Getting user cart for user: ' . $user['id']);
            $cart = $this->cartModel->getUserCart($user['id']);
            error_log('PaymentController::createPhonePeOrder - Cart retrieved: ' . json_encode($cart));

            if (!$cart || empty($cart['items'])) {
                error_log('PaymentController::createPhonePeOrder - Cart is empty or not found');
                Response::error('Your cart is empty. Please add items to your cart before proceeding to payment.', 400);
                return;
            }

            // Validate amount matches cart total
            $expectedAmount = (int)($cart['total_amount'] * 100); // Convert to paise
            error_log('PaymentController::createPhonePeOrder - Amount validation - Expected: ' . $expectedAmount . ', Received: ' . (int)$input['amount']);

            if ((int)$input['amount'] !== $expectedAmount) {
                error_log('PaymentController::createPhonePeOrder - Amount mismatch error');
                Response::error('Amount mismatch', 400);
                return;
            }

            // Prepare PhonePe payment data
            $paymentData = [
                'amount' => $input['amount'], // Amount in paise
                'user_id' => $user['id'],
                'mobile_number' => $user['phone'] ?? null,
                'redirect_url' => $_ENV['FRONTEND_URL'] . '/payment/success',
                'callback_url' => $_ENV['APP_URL'] . '/api/v1/payments/callback'
            ];

            // For development: Use a dummy callback URL that PhonePe can reach
            if ($_ENV['APP_ENV'] === 'development') {
                // Use a webhook.site URL for testing - this allows PhonePe to send callbacks
                // The frontend will poll for payment status instead of relying on callback
                $paymentData['callback_url'] = 'https://webhook.site/c8f5e4a0-8b2a-4c5d-9e3f-1a2b3c4d5e6f';
            }

            error_log('PaymentController::createPhonePeOrder - Creating PhonePe payment request');
            // Create PhonePe payment request
            $phonePeResponse = $this->phonePeService->createPaymentRequest($paymentData);

            if ($phonePeResponse && $phonePeResponse['success']) {
                error_log('PaymentController::createPhonePeOrder - PhonePe order created successfully: ' . $phonePeResponse['merchantTransactionId']);
                Response::success([
                    'payment_url' => $phonePeResponse['paymentUrl'],
                    'merchant_transaction_id' => $phonePeResponse['merchantTransactionId'],
                    'transaction_id' => $phonePeResponse['transactionId'],
                    'amount' => $input['amount'],
                    'merchant_config' => $this->phonePeService->getMerchantConfig()
                ]);
            } else {
                $errorMessage = $phonePeResponse['error'] ?? 'Failed to create payment order';
                error_log('PaymentController::createPhonePeOrder - Failed to create PhonePe order: ' . $errorMessage);
                Response::error($errorMessage, 500);
            }

        } catch (\Exception $e) {
            error_log('PaymentController::createPhonePeOrder - Exception caught: ' . $e->getMessage());
            error_log('PaymentController::createPhonePeOrder - Exception trace: ' . $e->getTraceAsString());
            Response::error('Payment order creation failed: ' . $e->getMessage(), 500);
        }
    }

/**
 * Verify PhonePe payment and create order
 * POST /api/v1/payments/verify
 */
public function verifyPayment(): void
{
    try {
        $user = AuthMiddleware::getCurrentUserData();
        if (!$user) {
            error_log('PaymentController::verifyPayment - Authentication failed');
            Response::error('Authentication required', 401);
            return;
        }

        $input = json_decode(file_get_contents('php://input'), true);
        error_log('PaymentController::verifyPayment - Input received: ' . json_encode($input));

        // Validate required fields for PhonePe
        $required = ['merchant_transaction_id'];
        foreach ($required as $field) {
            if (empty($input[$field])) {
                error_log("PaymentController::verifyPayment - Missing field: {$field}");
                Response::error("Missing required field: {$field}", 400);
                return;
            }
        }

        // order_data is optional (might not be available if localStorage was cleared)
        $orderData = $input['order_data'] ?? null;
        if ($orderData) {
            error_log('PaymentController::verifyPayment - Order data provided from localStorage');
        } else {
            error_log('PaymentController::verifyPayment - No order data provided, will use cart data');
        }

        // Verify payment with PhonePe
        error_log('PaymentController::verifyPayment - Verifying payment: ' . $input['merchant_transaction_id']);
        $verificationResult = $this->phonePeService->verifyPayment($input['merchant_transaction_id']);

        error_log('PaymentController::verifyPayment - Verification result: ' . json_encode($verificationResult));

        if (!$verificationResult || !$verificationResult['success']) {
            $errorMessage = $verificationResult['error'] ?? 'Payment verification failed';
            error_log('PaymentController::verifyPayment - Verification failed: ' . $errorMessage);
            Response::error($errorMessage, 400);
            return;
        }

        // ✅ Fixed: Check for both COMPLETED and SUCCESS status
        $paymentStatus = strtoupper($verificationResult['status'] ?? '');
        if (!in_array($paymentStatus, ['COMPLETED', 'SUCCESS'])) {
            error_log('PaymentController::verifyPayment - Payment not successful. Status: ' . $paymentStatus);
            Response::error('Payment not completed. Status: ' . $paymentStatus, 400);
            return;
        }

        // Get user's cart
        error_log('PaymentController::verifyPayment - Getting user cart for user: ' . $user['id']);
        $cart = $this->cartModel->getUserCart($user['id']);
        if (!$cart || empty($cart['items'])) {
            error_log('PaymentController::verifyPayment - Cart is empty');
            Response::error('Cart is empty', 400);
            return;
        }

        // Create order with payment details
        // Use provided order_data if available, otherwise create minimal order data
        $orderDataToCreate = $orderData ?? [];
        $orderDataToCreate['user_id'] = $user['id'];
        $orderDataToCreate['customer_email'] = $user['email'];
        $orderDataToCreate['customer_phone'] = $user['phone'];
        $orderDataToCreate['customer_name'] = $orderDataToCreate['customer_name'] ??
                                      (trim(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? '')) ?: 'Customer');
        $orderDataToCreate['payment_method'] = 'phonepe';
        $orderDataToCreate['subtotal'] = $cart['subtotal'];
        $orderDataToCreate['tax_amount'] = $cart['tax_amount'] ?? 0;
        $orderDataToCreate['discount_amount'] = $cart['discount_amount'] ?? 0;
        $orderDataToCreate['total_amount'] = $cart['total_amount'];
        $orderDataToCreate['coupon_code'] = $cart['coupon_code'] ?? null;

        // Handle shipping address - use from order_data if available, otherwise get user's default
        if ($orderData && isset($orderData['shipping_address'])) {
            $orderDataToCreate['shipping_address'] = $orderData['shipping_address'];
            error_log('PaymentController::verifyPayment - Using shipping address from order data');
        } else {
            // Fallback: try to get user's default address
            error_log('PaymentController::verifyPayment - No shipping address in order data, trying user default');
            $defaultAddress = $this->addressModel->getDefaultAddress($user['id'], 'shipping');
            if ($defaultAddress) {
                $orderDataToCreate['shipping_address'] = [
                    'first_name' => $defaultAddress['first_name'],
                    'last_name' => $defaultAddress['last_name'],
                    'line1' => $defaultAddress['address_line_1'],
                    'line2' => $defaultAddress['address_line_2'],
                    'city' => $defaultAddress['city'],
                    'state' => $defaultAddress['state'],
                    'postal_code' => $defaultAddress['postal_code'],
                    'country' => $defaultAddress['country'],
                    'phone' => $defaultAddress['phone']
                ];
                error_log('PaymentController::verifyPayment - Using user default shipping address');
            } else {
                error_log('PaymentController::verifyPayment - No default address found for user');
            }
        }

        error_log('PaymentController::verifyPayment - Creating order with data: ' . json_encode($orderDataToCreate));
        $orderId = $this->orderModel->createOrder($orderDataToCreate, $cart['items']);

        if ($orderId) {
            error_log('PaymentController::verifyPayment - Order created successfully: ' . $orderId);
            
            // Update order with PhonePe payment details
            $this->orderModel->updatePaymentStatus($orderId, 'paid', [
                'payment_id' => $verificationResult['transactionId'],
                'signature' => $input['merchant_transaction_id']
            ]);

            // Update order status to confirmed
            $this->orderModel->updateOrderStatus($orderId, 'confirmed');

            // Clear cart
            $this->cartModel->clearCart($user['id']);

            // Get created order details
            $order = $this->orderModel->getOrderById($orderId);
            $orderItems = $this->orderModel->getOrderItems($orderId);
            $userDetails = $this->userModel->findById($user['id']);

            // Send notifications asynchronously (only after payment is confirmed)
            error_log("PaymentController::verifyPayment - Sending notifications for order: {$order['order_number']}");
            $this->sendOrderNotifications($order, $userDetails, $orderItems);

            Response::success([
                'order_id' => $orderId,
                'order_number' => $order['order_number'],
                'payment_status' => 'paid',
                'total_amount' => $order['total_amount'],
                'message' => 'Payment successful and order created'
            ]);
        } else {
            error_log('PaymentController::verifyPayment - Failed to create order');
            Response::error('Failed to create order after payment', 500);
        }

    } catch (\Exception $e) {
        error_log('PaymentController::verifyPayment - Exception: ' . $e->getMessage());
        error_log('PaymentController::verifyPayment - Trace: ' . $e->getTraceAsString());
        Response::error('Payment verification failed: ' . $e->getMessage(), 500);
    }
}


    /**
     * Handle PhonePe payment callback
     * POST /api/v1/payments/callback
     */
    public function handlePhonePeCallback(): void
    {
        try {
            // Handle both GET and POST requests
            $input = [];

            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $rawInput = file_get_contents('php://input');
                error_log('PhonePe Callback POST data: ' . $rawInput);

                // Try to parse as JSON first
                $jsonInput = json_decode($rawInput, true);
                if ($jsonInput) {
                    $input = $jsonInput;
                } else {
                    // Parse as form data
                    parse_str($rawInput, $input);
                }
            } else {
                // GET request - get from query parameters
                $input = $_GET;
            }

            error_log('PhonePe Callback received: ' . json_encode($input));
            error_log('PhonePe Callback method: ' . $_SERVER['REQUEST_METHOD']);
            error_log('PhonePe Callback headers: ' . json_encode(getallheaders()));

            // For now, just return success to PhonePe
            // The actual verification will be done when frontend calls verify endpoint
            http_response_code(200);
            echo json_encode(['success' => true, 'message' => 'Callback received']);
            exit;

        } catch (\Exception $e) {
            error_log('PhonePe Callback Error: ' . $e->getMessage());
            http_response_code(200); // Still return 200 to PhonePe
            echo json_encode(['success' => true, 'message' => 'Callback processed']);
            exit;
        }
    }

    /**
     * Check PhonePe payment status (for polling)
     * GET /api/v1/payments/phonepe/status/{merchantTransactionId}
     */
    public function checkPhonePePaymentStatus(array $params): void
    {
        try {
            $merchantTransactionId = $params['merchantTransactionId'] ?? '';
            if (empty($merchantTransactionId)) {
                Response::error('Merchant transaction ID required', 400);
                return;
            }

            error_log('PaymentController::checkPhonePePaymentStatus - Checking status for: ' . $merchantTransactionId);

            // Check payment status with PhonePe
            $statusResponse = $this->phonePeService->verifyPayment($merchantTransactionId);

            if ($statusResponse && $statusResponse['success']) {
                Response::success([
                    'payment_status' => strtolower($statusResponse['status'] ?? 'pending'),
                    'transaction_id' => $statusResponse['transactionId'] ?? null,
                    'amount' => $statusResponse['amount'] ?? null,
                    'payment_instrument' => $statusResponse['paymentInstrument'] ?? null
                ]);
            } else {
                Response::error('Failed to check payment status', 500);
            }

        } catch (\Exception $e) {
            error_log('PaymentController::checkPhonePePaymentStatus - Error: ' . $e->getMessage());
            Response::error('Payment status check failed', 500);
        }
    }

    /**
     * Handle payment failure
     * POST /api/v1/payments/failure
     */
    public function handlePaymentFailure(): void
    {
        try {
            $user = AuthMiddleware::getCurrentUserData();
            if (!$user) {
                Response::error('Authentication required', 401);
                return;
            }

            $input = json_decode(file_get_contents('php://input'), true);

            // Log payment failure
            error_log('Payment failed for user: ' . $user['id'] . ', Error: ' . json_encode($input));

            Response::success([
                'message' => 'Payment failure recorded',
                'redirect_to_cart' => true
            ]);

        } catch (\Exception $e) {
            error_log('Handle payment failure error: ' . $e->getMessage());
            Response::error('Failed to handle payment failure', 500);
        }
    }

    /**
     * Get payment status
     * GET /api/v1/payments/{payment_id}/status
     */
    public function getPaymentStatus(array $params): void
    {
        try {
            $paymentId = $params['payment_id'];
            
            // Get payment details from Razorpay
            $paymentDetails = $this->getRazorpayPaymentDetails($paymentId);

            if ($paymentDetails) {
                Response::success([
                    'payment_id' => $paymentDetails['id'],
                    'status' => $paymentDetails['status'],
                    'amount' => $paymentDetails['amount'],
                    'currency' => $paymentDetails['currency'],
                    'created_at' => $paymentDetails['created_at']
                ]);
            } else {
                Response::error('Payment not found', 404);
            }

        } catch (\Exception $e) {
            error_log('Get payment status failed: ' . $e->getMessage());
            Response::error('Failed to get payment status', 500);
        }
    }

    /**
     * Create Razorpay order via API
     */
    private function createRazorpayOrderAPI(array $orderData): ?array
    {
        $url = 'https://api.razorpay.com/v1/orders';
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($orderData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Basic ' . base64_encode($this->razorpayKeyId . ':' . $this->razorpayKeySecret)
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode === 200) {
            return json_decode($response, true);
        }

        error_log('Razorpay order creation failed: ' . $response);
        return null;
    }

    /**
     * Verify Razorpay payment signature
     */
    private function verifyRazorpaySignature(string $orderId, string $paymentId, string $signature): bool
    {
        $expectedSignature = hash_hmac('sha256', $orderId . '|' . $paymentId, $this->razorpayKeySecret);
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Get Razorpay payment details
     */
    private function getRazorpayPaymentDetails(string $paymentId): ?array
    {
        $url = "https://api.razorpay.com/v1/payments/{$paymentId}";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Basic ' . base64_encode($this->razorpayKeyId . ':' . $this->razorpayKeySecret)
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode === 200) {
            return json_decode($response, true);
        }

        return null;
    }

    /**
     * Send order notifications (SMS, Email, Invoice)
     */
    private function sendOrderNotifications(array $order, array $user, array $orderItems): void
    {
        try {
            error_log("sendOrderNotifications called for order: {$order['order_number']}");
            error_log("Order status: {$order['status']}, Payment status: {$order['payment_status']}");

            // Only send notifications if order status is 'confirmed' and payment is 'paid'
            if ($order['status'] !== 'confirmed' || $order['payment_status'] !== 'paid') {
                error_log("Skipping notifications - order not confirmed or payment not paid");
                return;
            }

            // Send SMS confirmation (async) - temporarily disabled to focus on email
            // $this->notificationService->sendOrderConfirmationSMS($order, $user);

            // Send email confirmation (async)
            $this->notificationService->sendOrderConfirmationEmail($order, $user, $orderItems);

            // Send tax invoice email (async)
            $this->notificationService->sendTaxInvoiceEmail($order, $user, $orderItems);

            // Also send direct email confirmation (backup)
            try {
                $emailSent = $this->emailService->send(
                    $user['email'],
                    "🎉 Order Confirmation - {$order['order_number']} | Wolffoxx",
                    'emails/order-confirmation',
                    [
                        'order' => $order,
                        'user' => $user,
                        'orderItems' => $orderItems
                    ]
                );

                if ($emailSent) {
                    error_log("Direct order confirmation email sent successfully for order {$order['order_number']} to {$user['email']}");
                } else {
                    error_log("Failed to send direct order confirmation email for order {$order['order_number']} to {$user['email']}");
                }

                // Send admin copy
                $adminEmail = $_ENV['ADMIN_EMAIL'] ?? '<EMAIL>';
                if (!empty($adminEmail) && filter_var($adminEmail, FILTER_VALIDATE_EMAIL)) {
                    $adminEmailSent = $this->emailService->send(
                        $adminEmail,
                        "🛎️ New Order Received - {$order['order_number']}",
                        'emails/order-confirmation',
                        [
                            'order' => $order,
                            'user' => $user,
                            'orderItems' => $orderItems,
                            'is_admin_copy' => true
                        ]
                    );

                    if ($adminEmailSent) {
                        error_log("Admin order notification sent successfully for order {$order['order_number']} to {$adminEmail}");
                    } else {
                        error_log("Failed to send admin order notification for order {$order['order_number']} to {$adminEmail}");
                    }
                }
            } catch (\Exception $e) {
                error_log("Direct email sending failed: " . $e->getMessage());
            }

            error_log("Order notifications queued for order: {$order['order_number']}");

        } catch (\Exception $e) {
            error_log("Failed to queue order notifications: " . $e->getMessage());
            // Don't throw exception as this shouldn't block the payment flow
        }
    }
}
